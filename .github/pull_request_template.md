## Summary

Describe the purpose of this PR and the problem it solves.

## Changes
- [ ] Feature
- [ ] Fix
- [ ] Chore/Docs

What changed?

## Screenshots / Demos

If applicable, add screenshots or a Loom link.

## How to Test
Steps and commands to verify locally.

## Checklist
- [ ] PR title follows Conventional Commits (e.g., feat(auth): add Google provider)
- [ ] Added/updated tests (if any)
- [ ] Updated docs (if needed)
- [ ] No secrets committed; env vars documented in .env.example

