{"name": "cssdost", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "prebuild": "prisma generate", "postinstall": "prisma generate", "build": "prisma generate && next build", "vercel-build": "prisma generate && next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev --name init", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^6.13.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.45.3", "@tailwindcss/postcss": "^4.1.11", "lucide-react": "^0.539.0", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.4.4", "prisma": "^6.13.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.6", "typescript": "^5"}}