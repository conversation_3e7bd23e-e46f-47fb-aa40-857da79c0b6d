# Contributing

## Branching
- main: always deployable
- feat/*: features (e.g., feat/auth-google)
- fix/*: bug fixes
- chore/*: tooling, docs

## Commit Messages
Use Conventional Commits:
- feat(scope): summary
- fix(scope): summary
- chore(scope): summary
- docs(scope): summary

## PR Guidelines
- Small, focused changes
- Link to issue or user story
- Include testing steps and screenshots if UI changes

## Running Locally
```bash
npm i
npm run db:migrate # or db:push for schema only
npm run dev
```

## Environment
Copy `.env.example` to `.env` and fill values.

## Code Style
- TypeScript strictness
- Lint: `npm run lint`
- Avoid large diffs; match existing formatting
