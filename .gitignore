# Dependencies
node_modules/
.pnp/
.pnp.js

# Build outputs
.next/
out/
.turbo/

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
!.env.example

# Vercel
.vercel/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Misc
.DS_Store
*.tsbuildinfo
coverage/
dist/

# Prisma (local files)
prisma/*.db
prisma/*.sqlite
prisma/migrations/.DS_Store
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

/src/generated/prisma

# Mac
.DS_Store

# dotenv
.env
