// Auth utilities and types
import { createSupabaseServerClient } from '@/lib/supabase-server';

export async function getUser() {
  const supabase = await createSupabaseServerClient();
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error) {
    console.error('Error getting user:', error);
    return null;
  }
  
  return user;
}

export async function getSession() {
  const supabase = await createSupabaseServerClient();
  const { data: { session }, error } = await supabase.auth.getSession();
  
  if (error) {
    console.error('Error getting session:', error);
    return null;
  }
  
  return session;
}

export type AuthUser = {
  id: string;
  email: string;
  name?: string;
  role?: 'USER' | 'ADMIN';
};


