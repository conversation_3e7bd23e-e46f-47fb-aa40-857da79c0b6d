'use client';

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function ResetPasswordForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  
  const router = useRouter();

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    // Client-side password validation
    const validatePassword = (password: string) => {
      const errors: string[] = [];
      
      if (password.length < 8) {
        errors.push('at least 8 characters long');
      }
      
      if (!/[A-Z]/.test(password)) {
        errors.push('one uppercase letter');
      }
      
      if (!/[a-z]/.test(password)) {
        errors.push('one lowercase letter');
      }
      
      if (!/\d/.test(password)) {
        errors.push('one number');
      }
      
      if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        errors.push('one special character');
      }
      
      return errors;
    };

    const passwordErrors = validatePassword(password);
    
    if (passwordErrors.length > 0) {
      setError(`Password must contain ${passwordErrors.join(', ')}`);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/password/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(true);
        // Redirect to dashboard after 3 seconds
        setTimeout(() => {
          router.push('/dashboard');
        }, 3000);
      } else {
        setError(data.error?.message || 'An error occurred');
      }
    } catch (err) {
      // Log only safe error information, never sensitive details
      if (process.env.NODE_ENV !== 'production') {
        console.error('Password reset failed:', {
          type: err instanceof Error ? err.constructor.name : 'Unknown',
          timestamp: new Date().toISOString()
        });
      }
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className={cn("flex flex-col gap-6", className)} {...props}>
        <Card className="overflow-hidden p-0 bg-white">
          <CardContent className="grid p-0 md:grid-cols-2">
            <div className="p-6 md:p-8">
              <div className="flex flex-col gap-6 items-center justify-center h-full">
                <Image 
                  src="/images/logos/logo-dark.png" 
                  alt="CSS Dost Logo" 
                  width={100}
                  height={100}
                  className="mx-auto mb-2 md:hidden"
                />
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    Password Updated Successfully!
                  </h2>
                  <p className="text-sm text-muted-foreground mb-6">
                    Your password has been reset. You will be redirected to your dashboard shortly.
                  </p>
                  <Button asChild>
                    <Link href="/dashboard">
                      Go to Dashboard
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
            <div className="relative hidden md:block overflow-hidden">
              <div 
                className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                style={{
                  backgroundImage: "url('/images/backgrounds/banner.png')"
                }}
              >
                <div className="absolute inset-0 bg-black/30"></div>
              </div>
              <div className="absolute inset-0 flex items-center justify-center p-8">
                <div className="text-center text-white relative z-10">
                  <div className="mb-2 mx-auto w-fit">
                    <Image 
                      src="/images/logos/logo-dark.png" 
                      alt="CSS Dost Logo" 
                      width={140}
                      height={140}
                      className="drop-shadow-2xl"
                    />
                  </div>
                  <h2 className="text-3xl lg:text-4xl font-bold mb-4 drop-shadow-2xl">
                    Welcome Back!
                  </h2>
                  <p className="text-lg drop-shadow-xl">
                    Your password has been successfully updated
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden p-0 w-full bg-white">
        <CardContent className="grid p-0 md:grid-cols-2 min-h-[600px]">
          <form onSubmit={handlePasswordReset} className="p-6 md:p-8 flex flex-col justify-center">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <Image 
                  src="/images/logos/logo-dark.png" 
                  alt="CSS Dost Logo" 
                  width={100}
                  height={100}
                  className="mx-auto mb-4 md:hidden"
                />
                <h1 className="text-2xl font-bold">Reset Your Password</h1>
                <p className="text-muted-foreground text-balance">
                  Enter your new password below
                </p>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="text-red-800 text-sm">{error}</div>
                </div>
              )}

              <div className="grid gap-3">
                <Label htmlFor="password">New Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your new password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  minLength={8}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Must be at least 8 characters with uppercase, lowercase, number, and special character
                </p>
              </div>

              <div className="grid gap-3">
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="Confirm your new password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  minLength={8}
                  required
                />
              </div>

              <Button type="submit" className="w-full" style={{backgroundColor: '#2563eb', color: 'white'}} disabled={loading}>
                {loading ? 'Updating Password...' : 'Update Password'}
              </Button>

              <div className="text-center text-sm">
                Remember your password?{" "}
                <Link href="/auth/login" className="underline underline-offset-4">
                  Sign in
                </Link>
              </div>
            </div>
          </form>
          <div className="relative hidden md:block overflow-hidden">
            <div 
              className="absolute inset-0 bg-cover bg-center bg-no-repeat"
              style={{
                backgroundImage: "url('/images/backgrounds/banner.png')"
              }}
            >
              <div className="absolute inset-0 bg-black/30"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center p-8">
              <div className="text-center text-white relative z-10">
                <div className="mb-2 mx-auto w-fit">
                  <Image 
                    src="/images/logos/logo-dark.png" 
                    alt="CSS Dost Logo" 
                    width={140}
                    height={140}
                    className="drop-shadow-2xl"
                  />
                </div>
                <h2 className="text-3xl lg:text-4xl font-bold mb-4 drop-shadow-2xl">
                  Reset Your Password
                </h2>
                <p className="text-lg drop-shadow-xl">
                  Choose a strong password to secure your account
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
