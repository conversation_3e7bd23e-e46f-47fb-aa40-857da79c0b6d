'use client';

import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth-provider";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [shouldRedirect, setShouldRedirect] = useState(false);
  
  const { signIn, signInWithProvider, user, session } = useAuth();
  const router = useRouter();

  // Handle redirect when user is authenticated
  useEffect(() => {
    if (shouldRedirect && user && session) {
      router.push('/dashboard');
      setShouldRedirect(false);
    }
  }, [shouldRedirect, user, session, router]);

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const { error } = await signIn(email, password);
      
      if (error) {
        setError(error.message);
      } else {
        // Set flag to redirect once session is established
        setShouldRedirect(true);
      }
    } catch (err) {
      // Log only safe error information, never sensitive details
      if (process.env.NODE_ENV !== 'production') {
        console.error('Login failed:', {
          type: err instanceof Error ? err.constructor.name : 'Unknown',
          timestamp: new Date().toISOString()
        });
      }
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleProviderLogin = async (provider: 'google') => {
    const { error } = await signInWithProvider(provider);
    if (error) {
      setError(error.message);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden p-0 w-full bg-white">
        <CardContent className="grid p-0 md:grid-cols-2 min-h-[600px]">
          <form onSubmit={handleEmailLogin} className="p-6 md:p-8 flex flex-col justify-center">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <Image 
                  src="/images/logos/logo-dark.png" 
                  alt="CSS Dost Logo" 
                  width={100}
                  height={100}
                  className="mx-auto mb-4 md:hidden"
                />
                <h1 className="text-2xl font-bold">Welcome back</h1>
                <p className="text-muted-foreground text-balance">
                  Login to your CSS Dost account
                </p>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="text-red-800 text-sm">{error}</div>
                </div>
              )}

              <div className="grid gap-3">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-3">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="ml-auto text-sm underline-offset-2 hover:underline"
                  >
                    Forgot your password?
                  </Link>
                </div>
                <Input 
                  id="password" 
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required 
                />
              </div>
              <Button type="submit" className="w-full" style={{backgroundColor: '#2563eb', color: 'white'}} disabled={loading}>
                {loading ? 'Signing in...' : 'Login'}
              </Button>
              <div className="relative text-center text-sm">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">
                    Or continue with
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-1 gap-4">
                <Button 
                  variant="outline" 
                  type="button" 
                  className="w-full"
                  onClick={() => handleProviderLogin('google')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="w-4 h-4 mr-2">
                    <path
                      d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                      fill="currentColor"
                    />
                  </svg>
                  Continue with Google
                </Button>
              </div>
              <div className="text-center text-sm">
                Don&apos;t have an account?{" "}
                <Link href="/auth/signup" className="underline underline-offset-4">
                  Sign up
                </Link>
              </div>
            </div>
          </form>
          <div className="relative hidden md:block overflow-hidden">
            <div 
              className="absolute inset-0 bg-cover bg-center bg-no-repeat"
              style={{
                backgroundImage: "url('/images/backgrounds/banner.png')"
              }}
            >
              {/* Overlay for better text readability */}
              <div className="absolute inset-0 bg-black/30"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center p-8">
              <div className="text-center text-white relative z-10">
                <div className="mb-2 mx-auto w-fit">
                  <Image 
                    src="/images/logos/logo-dark.png" 
                    alt="CSS Dost Logo" 
                    width={140}
                    height={140}
                    className="drop-shadow-2xl"
                  />
                </div>
                <h2 className="text-3xl lg:text-4xl font-bold mb-4 drop-shadow-2xl">
                  Master CSS/PMS Exams
                </h2>
                <p className="text-lg drop-shadow-xl">
                  Your ultimate preparation platform for Civil Services of Pakistan
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="text-muted-foreground text-center text-xs text-balance">
        By clicking continue, you agree to our <Link href="/terms-of-service" className="underline underline-offset-4 hover:text-primary">Terms of Service</Link>{" "}
        and <Link href="/privacy-policy" className="underline underline-offset-4 hover:text-primary">Privacy Policy</Link>.
      </div>
    </div>
  )
}
