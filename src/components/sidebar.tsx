"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useAuth } from "@/components/auth-provider";
import {
  LayoutDashboard,
  BookOpen,
  FileText,
  TrendingUp,
  MessageSquare,
  FolderOpen,
  Settings,
  LogOut,
  User,
  Target,
  Calendar,
  Award,
  BarChart3,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface SidebarProps {
  className?: string;
}

export function DashboardSidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const { user, signOut } = useAuth();

  const navigation = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "Subjects",
      href: "/subjects",
      icon: BookOpen,
    },
    {
      title: "Mock Tests",
      href: "/tests",
      icon: FileText,
    },
    {
      title: "Progress",
      href: "/progress",
      icon: TrendingUp,
    },
    {
      title: "Forum",
      href: "/forum",
      icon: MessageSquare,
    },
    {
      title: "Study Materials",
      href: "/materials",
      icon: FolderOpen,
    },
  ];

  const handleSignOut = async () => {
    await signOut();
  };

  const getUserInitials = (name?: string, email?: string) => {
    if (name) {
      return name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email.slice(0, 2).toUpperCase();
    }
    return "U";
  };

  const maskEmail = (email?: string) => {
    if (!email) return "<EMAIL>";
    const [username, domain] = email.split("@");
    if (username.length <= 2) return email;
    const maskedUsername = username[0] + "*".repeat(username.length - 2) + username[username.length - 1];
    return `${maskedUsername}@${domain}`;
  };

  const getDisplayName = (name?: string) => {
    if (!name) return "User";
    const firstName = name.split(" ")[0];
    return firstName.length > 10 ? firstName.slice(0, 10) + "..." : firstName;
  };

  return (
    <Sidebar className={`bg-gradient-to-b from-sidebar to-sidebar/95 ${className}`}>
      <SidebarHeader className="border-b border-border px-6 py-4">
        <div className="flex items-center gap-4">
          <div className="relative w-12 h-12 rounded-xl overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 p-2 shadow-lg">
            <Image
              src="/images/logos/logo-white.png"
              alt="CSS Dost Logo"
              width={40}
              height={40}
              className="w-full h-full object-contain"
            />
          </div>
          <div className="flex flex-col">
            <span className="font-bold text-xl text-sidebar-foreground">CSS Dost</span>
            <span className="text-sm text-sidebar-foreground/70 font-medium">Exam Preparation</span>
          </div>
        </div>
      </SidebarHeader>
        <SidebarContent className="px-4">
          <SidebarGroup>
            <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/60 uppercase tracking-wider mb-2">
              Search
            </SidebarGroupLabel>
            <SidebarInput
              placeholder="Search subjects, tests..."
              className="bg-sidebar-accent/50 border-sidebar-border focus:bg-sidebar-accent"
            />
          </SidebarGroup>

          <SidebarGroup className="mt-6">
            <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/60 uppercase tracking-wider mb-2">
              Navigation
            </SidebarGroupLabel>
            <SidebarMenu className="space-y-1">
              {navigation.map((item) => {
                const isActive = pathname === item.href || pathname.startsWith(item.href);
                return (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive}
                      className={`
                        relative rounded-lg transition-all duration-200 hover:bg-sidebar-accent
                        ${isActive
                          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md'
                          : 'text-sidebar-foreground hover:text-sidebar-accent-foreground'
                        }
                      `}
                    >
                      <Link href={item.href} className="flex items-center gap-3 px-3 py-2.5">
                        <item.icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-sidebar-foreground/70'}`} />
                        <span className="font-medium">{item.title}</span>
                        {isActive && (
                          <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-white rounded-l-full" />
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroup>
          <SidebarGroup className="mt-6">
            <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/60 uppercase tracking-wider mb-3">
              Quick Stats
            </SidebarGroupLabel>
            <div className="space-y-3">
              <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg p-3 border border-green-500/20">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-500/20 rounded-lg">
                    <BarChart3 className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-sidebar-foreground">12</p>
                    <p className="text-xs text-sidebar-foreground/60">Tests Completed</p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg p-3 border border-blue-500/20">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-lg">
                    <TrendingUp className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-sidebar-foreground">78%</p>
                    <p className="text-xs text-sidebar-foreground/60">Average Score</p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg p-3 border border-orange-500/20">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-500/20 rounded-lg">
                    <Calendar className="w-4 h-4 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-sidebar-foreground">7 days</p>
                    <p className="text-xs text-sidebar-foreground/60">Study Streak</p>
                  </div>
                </div>
              </div>
            </div>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter className="border-t border-border p-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-start h-auto p-3 rounded-lg hover:bg-sidebar-accent transition-colors"
              >
                <Avatar className="h-10 w-10 ring-2 ring-sidebar-border">
                  <AvatarImage src={user?.user_metadata?.avatar_url} />
                  <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                    {getUserInitials(user?.user_metadata?.name, user?.email)}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-3 text-left flex-1 min-w-0">
                  <p className="text-sm font-semibold text-sidebar-foreground truncate">
                    {getDisplayName(user?.user_metadata?.name)}
                  </p>
                  <p className="text-xs text-sidebar-foreground/60 truncate">
                    {maskEmail(user?.email)}
                  </p>
                </div>
                <Settings className="w-4 h-4 text-sidebar-foreground/40 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 bg-background border border-border shadow-lg" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user?.user_metadata?.name || "User"}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {maskEmail(user?.email)}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground cursor-pointer">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground cursor-pointer">
                <Target className="mr-2 h-4 w-4" />
                <span>Goals</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground cursor-pointer">
                <Calendar className="mr-2 h-4 w-4" />
                <span>Schedule</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground cursor-pointer">
                <Award className="mr-2 h-4 w-4" />
                <span>Achievements</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground cursor-pointer">
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleSignOut}
                className="hover:bg-destructive hover:text-destructive-foreground cursor-pointer text-destructive"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarFooter>
      </Sidebar>
  );
}
