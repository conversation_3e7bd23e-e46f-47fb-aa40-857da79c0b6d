'use client';

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function ForgotPasswordForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/password/reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(true);
      } else {
        setError(data.error?.message || 'An error occurred');
      }
    } catch (err) {
      // Log only safe error information, never sensitive details
      if (process.env.NODE_ENV !== 'production') {
        console.error('Password reset request failed:', {
          type: err instanceof Error ? err.constructor.name : 'Unknown',
          timestamp: new Date().toISOString()
        });
      }
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className={cn("flex flex-col gap-6", className)} {...props}>
        <Card className="overflow-hidden p-0 bg-white">
          <CardContent className="grid p-0 md:grid-cols-2">
            <div className="p-6 md:p-8">
              <div className="flex flex-col gap-6 items-center justify-center h-full">
                <Image 
                  src="/images/logos/logo-dark.png" 
                  alt="CSS Dost Logo" 
                  width={100}
                  height={100}
                  className="mx-auto mb-2 md:hidden"
                />
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    Check Your Email
                  </h2>
                  <p className="text-sm text-muted-foreground mb-6">
                    We&apos;ve sent a password reset link to<br />
                    <strong className="text-gray-900">{email}</strong>
                  </p>
                  <p className="text-xs text-muted-foreground mb-6">
                    If you don&apos;t see the email, check your spam folder.
                  </p>
                  <Button asChild>
                    <Link href="/auth/login">
                      Back to Login
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
            <div className="relative hidden md:block overflow-hidden">
              <div 
                className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                style={{
                  backgroundImage: "url('/images/backgrounds/banner.png')"
                }}
              >
                <div className="absolute inset-0 bg-black/30"></div>
              </div>
              <div className="absolute inset-0 flex items-center justify-center p-8">
                <div className="text-center text-white relative z-10">
                  <div className="mb-2 mx-auto w-fit">
                    <Image 
                      src="/images/logos/logo-dark.png" 
                      alt="CSS Dost Logo" 
                      width={140}
                      height={140}
                      className="drop-shadow-2xl"
                    />
                  </div>
                  <h2 className="text-3xl lg:text-4xl font-bold mb-4 drop-shadow-2xl">
                    Reset Link Sent!
                  </h2>
                  <p className="text-lg drop-shadow-xl">
                    Check your email for the password reset link
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden p-0 w-full bg-white">
        <CardContent className="grid p-0 md:grid-cols-2 min-h-[600px]">
          <form onSubmit={handleSubmit} className="p-6 md:p-8 flex flex-col justify-center">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <Image 
                  src="/images/logos/logo-dark.png" 
                  alt="CSS Dost Logo" 
                  width={100}
                  height={100}
                  className="mx-auto mb-4 md:hidden"
                />
                <h1 className="text-2xl font-bold">Forgot Your Password?</h1>
                <p className="text-muted-foreground text-balance">
                  Enter your email address and we&apos;ll send you a link to reset your password.
                </p>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="text-red-800 text-sm">{error}</div>
                </div>
              )}

              <div className="grid gap-3">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>

              <Button type="submit" className="w-full" style={{backgroundColor: '#2563eb', color: 'white'}} disabled={loading}>
                {loading ? 'Sending Reset Link...' : 'Send Reset Link'}
              </Button>

              <div className="text-center text-sm">
                Remember your password?{" "}
                <Link href="/auth/login" className="underline underline-offset-4">
                  Sign in
                </Link>
              </div>
            </div>
          </form>
          <div className="relative hidden md:block overflow-hidden">
            <div 
              className="absolute inset-0 bg-cover bg-center bg-no-repeat"
              style={{
                backgroundImage: "url('/images/backgrounds/banner.png')"
              }}
            >
              <div className="absolute inset-0 bg-black/30"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center p-8">
              <div className="text-center text-white relative z-10">
                <div className="mb-2 mx-auto w-fit">
                  <Image 
                    src="/images/logos/logo-dark.png" 
                    alt="CSS Dost Logo" 
                    width={140}
                    height={140}
                    className="drop-shadow-2xl"
                  />
                </div>
                <h2 className="text-3xl lg:text-4xl font-bold mb-4 drop-shadow-2xl">
                  Forgot Password?
                </h2>
                <p className="text-lg drop-shadow-xl">
                  No worries! We&apos;ll help you get back into your account
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
