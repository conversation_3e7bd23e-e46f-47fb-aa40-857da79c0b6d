"use client";

import { ReactNode } from "react";
import { DashboardSidebar } from "@/components/sidebar";
import { Head<PERSON> } from "@/components/header";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";

interface DashboardLayoutProps {
  children: ReactNode;
  className?: string;
}

export function DashboardLayout({ children, className }: DashboardLayoutProps) {
  return (
    <SidebarProvider>
      <div className="flex h-screen">
        {/* Sidebar */}
        <DashboardSidebar />
        
        {/* Main Content Area */}
        <SidebarInset>
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header */}
            <Header />
            
            {/* Main Content */}
            <main className={`flex-1 overflow-y-auto p-6 ${className}`}>
              {children}
            </main>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
