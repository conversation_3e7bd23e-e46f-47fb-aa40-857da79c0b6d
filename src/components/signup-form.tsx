'use client';

import { useState } from "react";
import { useAuth } from "@/components/auth-provider";

import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function SignupForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [passwordErrors, setPasswordErrors] = useState<string[]>([]);
  
  const { signUp, signInWithProvider } = useAuth();

  // Password validation function
  const validatePassword = (password: string): string[] => {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('At least 8 characters');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('One uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('One lowercase letter');
    }
    if (!/\d/.test(password)) {
      errors.push('One number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('One special character');
    }
    
    return errors;
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    setPasswordErrors(validatePassword(newPassword));
  };


  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validate password before submission
    const validationErrors = validatePassword(password);
    if (validationErrors.length > 0) {
      setError(`Password requirements not met: ${validationErrors.join(', ')}`);
      setLoading(false);
      return;
    }

    try {
      const { error } = await signUp(email, password, {
        data: {
          name: `${firstName} ${lastName}`,
        }
      });
      
      if (error) {
        setError(error.message);
      } else {
        setSuccess(true);
      }
    } catch {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleProviderLogin = async (provider: 'google') => {
    const { error } = await signInWithProvider(provider);
    if (error) {
      setError(error.message);
    }
  };

  if (success) {
    return (
      <div className={cn("flex flex-col gap-6", className)} {...props}>
        <Card className="overflow-hidden p-0 bg-white">
          <CardContent className="grid p-0 md:grid-cols-2">
            <div className="p-6 md:p-8">
              <div className="flex flex-col gap-6 items-center justify-center h-full">
                <Image 
                  src="/images/logos/logo-dark.png" 
                  alt="CSS Dost Logo" 
                  width={100}
                  height={100}
                  className="mx-auto mb-2 md:hidden"
                />
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    Check your email
                  </h2>
                  <p className="text-sm text-muted-foreground mb-6">
                    We&apos;ve sent you a confirmation link at<br />
                    <strong className="text-gray-900">{email}</strong>
                  </p>
                  <Button asChild>
                    <Link href="/auth/login">
                      Back to login
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
            <div className="relative hidden md:block overflow-hidden">
              <div 
                className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                style={{
                  backgroundImage: "url('/images/backgrounds/banner Background Removed.png')"
                }}
              >
                {/* Overlay for better text readability */}
                <div className="absolute inset-0 bg-black/30"></div>
              </div>
              <div className="absolute inset-0 flex items-center justify-center p-8">
                <div className="text-center text-white relative z-10">
                  <div className="bg-white/30 p-4 rounded-2xl mb-6 mx-auto w-fit">
                    <Image 
                      src="/images/logos/logo-dark.png" 
                      alt="CSS Dost Logo" 
                      width={120}
                      height={120}
                      className="drop-shadow-2xl"
                    />
                  </div>
                  <h2 className="text-3xl lg:text-4xl font-bold mb-4 drop-shadow-2xl">
                    Welcome to CSS Dost!
                  </h2>
                  <p className="text-lg drop-shadow-xl">
                    Your confirmation email is on its way
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden p-0 w-full bg-white">
        <CardContent className="grid p-0 md:grid-cols-2 min-h-[600px]">
          <form onSubmit={handleSignup} className="p-6 md:p-8 flex flex-col justify-center">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <Image 
                  src="/images/logos/logo-dark.png" 
                  alt="CSS Dost Logo" 
                  width={100}
                  height={100}
                  className="mx-auto mb-4 md:hidden"
                />
                <h1 className="text-2xl font-bold">Create your account</h1>
                <p className="text-muted-foreground text-balance">
                  Join CSS Dost and start your preparation journey
                </p>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="text-red-800 text-sm">{error}</div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-3">
                  <Label htmlFor="firstName">First name</Label>
                  <Input
                    id="firstName"
                    type="text"
                    placeholder="John"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    required
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="lastName">Last name</Label>
                  <Input
                    id="lastName"
                    type="text"
                    placeholder="Doe"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="grid gap-3">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>

              <div className="grid gap-3">
                <Label htmlFor="password">Password</Label>
                <Input 
                  id="password" 
                  type="password"
                  placeholder="Create a strong password"
                  value={password}
                  onChange={handlePasswordChange}
                  minLength={8}
                  required 
                />
                <div className="space-y-1">
                  <p className="text-xs text-muted-foreground">
                    Must be at least 8 characters with uppercase, lowercase, number, and special character
                  </p>
                  {password.length > 0 && (
                    <div className="space-y-1">
                      {[
                        { label: 'At least 8 characters', test: password.length >= 8 },
                        { label: 'One uppercase letter', test: /[A-Z]/.test(password) },
                        { label: 'One lowercase letter', test: /[a-z]/.test(password) },
                        { label: 'One number', test: /\d/.test(password) },
                        { label: 'One special character', test: /[!@#$%^&*(),.?":{}|<>]/.test(password) }
                      ].map((requirement, index) => (
                        <div key={index} className="flex items-center gap-2 text-xs">
                          <div className={`w-2 h-2 rounded-full ${requirement.test ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                          <span className={requirement.test ? 'text-green-600' : 'text-gray-500'}>
                            {requirement.label}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <input
                    id="terms"
                    type="checkbox"
                    required
                    className="h-4 w-4 rounded border-gray-300 mt-0.5"
                  />
                  <div className="text-sm space-y-1">
                    <div>I agree to the following:</div>
                    <div className="flex flex-wrap gap-2 text-muted-foreground">
                      <Link href="/terms-of-service" className="underline underline-offset-4 hover:text-primary">
                        Terms of Service
                      </Link>
                      <span>and</span>
                      <Link href="/privacy-policy" className="underline underline-offset-4 hover:text-primary">
                        Privacy Policy
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              <Button type="submit" className="w-full" style={{backgroundColor: '#2563eb', color: 'white'}} disabled={loading}>
                {loading ? 'Creating account...' : 'Create Account'}
              </Button>

              <div className="relative text-center text-sm">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">
                    Or continue with
                  </span>
                </div>
              </div>

              <Button 
                variant="outline" 
                type="button" 
                className="w-full"
                onClick={() => handleProviderLogin('google')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="w-4 h-4 mr-2">
                  <path
                    d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                    fill="currentColor"
                  />
                </svg>
                Continue with Google
              </Button>

              <div className="text-center text-sm">
                Already have an account?{" "}
                <Link href="/auth/login" className="underline underline-offset-4">
                  Sign in
                </Link>
              </div>
            </div>
          </form>
          <div className="relative hidden md:block overflow-hidden">
            <div 
              className="absolute inset-0 bg-cover bg-center bg-no-repeat"
              style={{
                backgroundImage: "url('/images/backgrounds/banner.png')"
              }}
            >
              {/* Overlay for better text readability */}
              <div className="absolute inset-0 bg-black/30"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center p-8">
              <div className="text-center text-white relative z-10">
                <div className="mb-2 mx-auto w-fit">
                  <Image 
                    src="/images/logos/logo-dark.png" 
                    alt="CSS Dost Logo" 
                    width={140}
                    height={140}
                    className="drop-shadow-2xl"
                  />
                </div>
                <h2 className="text-3xl lg:text-4xl font-bold mb-4 drop-shadow-2xl">
                  Start Your Journey
                </h2>
                <p className="text-lg drop-shadow-xl">
                  Join thousands of students preparing for CSS/PMS exams
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="text-muted-foreground text-center text-xs text-balance">
        By creating an account, you agree to our <Link href="/terms-of-service" className="underline underline-offset-4 hover:text-primary">Terms of Service</Link>{" "}
        and <Link href="/privacy-policy" className="underline underline-offset-4 hover:text-primary">Privacy Policy</Link>.
      </div>
    </div>
  )
}
