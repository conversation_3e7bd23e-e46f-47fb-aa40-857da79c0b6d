"use client";
import Link from "next/link";
import { useAuth } from "@/components/auth-provider";

export default function Home() {
  const { user, signOut, loading } = useAuth();
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      {/* Navigation Header */}
      <nav className="flex items-center justify-between p-6 lg:px-8">
        <div className="flex items-center">
          <h1 className="text-2xl font-bold text-gray-900">CSS Dost</h1>
        </div>
        
        <div className="flex items-center gap-4">
          {loading ? (
            <div className="text-sm text-gray-500">Loading...</div>
          ) : user ? (
            <>
              <span className="text-sm text-gray-600">
                Welcome, {user.user_metadata?.name || user.email?.split('@')[0]}!
              </span>
              <Link
                href="/dashboard"
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors"
              >
                Dashboard
              </Link>
              <button
                onClick={signOut}
                className="text-gray-600 hover:text-gray-900 text-sm font-medium"
              >
                Sign out
              </button>
            </>
          ) : (
            <>
              <Link
                href="/auth/login"
                className="text-gray-600 hover:text-gray-900 text-sm font-medium"
              >
                Sign In
              </Link>
              <Link
                href="/auth/signup"
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors"
              >
                Sign Up
              </Link>
            </>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="py-24 sm:py-32 lg:pb-40">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Ace CSS/PMS with{' '}
              <span className="text-indigo-600">CSS Dost</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto">
              Your ultimate preparation platform for Civil Services of Pakistan. Master CSS/PMS exams with 
              comprehensive study materials, mock tests, and expert guidance to achieve your civil service dreams.
            </p>
            
            {!user && (
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Link
                  href="/auth/signup"
                  className="rounded-lg bg-indigo-600 px-6 py-3 text-lg font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-colors"
                >
                  Get Started Free
                </Link>
                <Link
                  href="/auth/login"
                  className="text-lg font-semibold leading-6 text-gray-900 hover:text-indigo-600 transition-colors"
                >
                  Sign in <span aria-hidden="true">→</span>
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Features Section */}
        <div className="py-24 sm:py-32">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Everything you need for CSS/PMS Success
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Comprehensive preparation tools designed for civil service aspirants
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div className="h-5 w-5 flex-none bg-indigo-600 rounded-full" />
                    Comprehensive Study Materials
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Access curated study materials, PDFs, and video lectures covering all CSS/PMS subjects from expert educators.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div className="h-5 w-5 flex-none bg-indigo-600 rounded-full" />
                    Mock Tests & Practice
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Take realistic mock tests, practice previous year papers, and get detailed performance analytics to track your progress.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div className="h-5 w-5 flex-none bg-indigo-600 rounded-full" />
                    Community & Discussion
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Connect with fellow aspirants, discuss exam strategies, and get guidance from successful CSS/PMS officers.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200">
        <div className="mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8">
          <div className="flex justify-center space-x-6 md:order-2">
            <p className="text-xs leading-5 text-gray-500">
              Built for CSS/PMS aspirants, by civil service experts
            </p>
          </div>
          <div className="mt-8 md:order-1 md:mt-0">
            <p className="text-xs leading-5 text-gray-500">
              &copy; 2024 CSS Dost. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
