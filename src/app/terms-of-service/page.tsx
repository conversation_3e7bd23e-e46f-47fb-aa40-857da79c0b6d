import Link from "next/link";

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow-sm rounded-lg p-8">
          <div className="mb-8">
            <Link 
              href="/auth/signup" 
              className="text-indigo-600 hover:text-indigo-500 text-sm font-medium"
            >
              ← Back to Sign Up
            </Link>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Terms of Service</h1>
          
          <div className="prose prose-gray max-w-none">
            <p className="text-gray-600 mb-6">
              <strong>Last updated:</strong> {new Date().toLocaleDateString()}
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">1. Agreement to Terms</h2>
            <p className="text-gray-700 mb-4">
              By accessing and using CSS Dost, you accept and agree to be bound by the terms and provision of this agreement.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">2. Service Description</h2>
            <p className="text-gray-700 mb-4">
              CSS Dost is a comprehensive preparation platform for Civil Services of Pakistan (CSS/PMS) examinations, 
              providing study materials, mock tests, and educational resources.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">3. User Accounts</h2>
            <p className="text-gray-700 mb-4">
              To access certain features, you must create an account. You are responsible for maintaining the 
              confidentiality of your account credentials and for all activities under your account.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">4. Acceptable Use</h2>
            <p className="text-gray-700 mb-4">
              You agree to use our services only for lawful purposes and in accordance with these Terms. 
              You may not use our services to:
            </p>
            <ul className="list-disc pl-6 text-gray-700 mb-4">
              <li>Violate any laws or regulations</li>
              <li>Infringe on intellectual property rights</li>
              <li>Share account credentials with others</li>
              <li>Attempt to gain unauthorized access to our systems</li>
            </ul>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">5. Intellectual Property</h2>
            <p className="text-gray-700 mb-4">
              All content, materials, and resources provided through CSS Dost are protected by intellectual 
              property laws and remain the property of CSS Dost or its licensors.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">6. Limitation of Liability</h2>
            <p className="text-gray-700 mb-4">
              CSS Dost shall not be liable for any indirect, incidental, special, or consequential damages 
              arising from your use of our services.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">7. Termination</h2>
            <p className="text-gray-700 mb-4">
              We may terminate or suspend your account and access to our services at our sole discretion, 
              without prior notice, for conduct that we believe violates these Terms.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">8. Changes to Terms</h2>
            <p className="text-gray-700 mb-4">
              We reserve the right to modify these terms at any time. We will notify users of any material 
              changes to these Terms.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">9. Contact Information</h2>
            <p className="text-gray-700 mb-4">
              If you have any questions about these Terms, please contact <NAME_EMAIL>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
