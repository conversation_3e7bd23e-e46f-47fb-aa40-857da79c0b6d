import prisma from "@/lib/prisma";

export async function GET() {
  try {
    const result = await prisma.$queryRaw<{ now: Date }[]>`SELECT NOW()`;
    return new Response(
      JSON.stringify({ ok: true, message: "DB reachable", result }),
      { status: 200, headers: { "content-type": "application/json" } }
    );
  } catch (error: unknown) {
    return new Response(
      JSON.stringify({ ok: false, message: "DB not reachable", error: String(error) }),
      { status: 500, headers: { "content-type": "application/json" } }
    );
  }
}


