import { createSupabaseServerClient } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json(
        { error: { code: 'MISSING_EMAIL', message: 'Email is required' } },
        { status: 400 }
      );
    }

    const supabase = await createSupabaseServerClient();
    
    // Ensure we have a valid site URL for production
    const siteUrl = process.env.SITE_URL || process.env.NEXT_PUBLIC_SITE_URL;
    if (!siteUrl) {
      console.error('SITE_URL environment variable is not set');
      return NextResponse.json(
        { message: 'If an account with that email exists, a password reset link has been sent.' },
        { status: 200 }
      );
    }
    
    // Use Supabase's built-in password reset functionality
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${siteUrl}/auth/reset-password`,
    });

    // Always return 200 to prevent user enumeration, even if there's an error
    if (error) {
      // Log only safe error information, not the full error object
      console.error('Password reset failed:', {
        code: error.name,
        message: error.message,
        status: error.status || 'unknown'
      });
      // Still return 200 to avoid revealing whether the email exists
      return NextResponse.json(
        { message: 'If an account with that email exists, a password reset link has been sent.' },
        { status: 200 }
      );
    }

    return NextResponse.json(
      { message: 'If an account with that email exists, a password reset link has been sent.' },
      { status: 200 }
    );
  } catch (error) {
    // Log only safe error information, not the full error object
    console.error('Password reset request failed:', {
      type: error instanceof Error ? error.constructor.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
    return NextResponse.json(
      { message: 'If an account with that email exists, a password reset link has been sent.' },
      { status: 200 }
    );
  }
}
