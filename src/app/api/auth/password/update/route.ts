import { createSupabaseServerClient } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';

// Function to sanitize error messages for client consumption
const sanitizeErrorMessage = (error: { message?: string; name?: string; status?: number }): string => {
  // Map known Supabase error codes and exact messages to user-friendly messages
  const errorMessageMap: Record<string, string> = {
    // Exact error messages
    'Invalid refresh token': 'Your session has expired. Please request a new password reset link.',
    'User not found': 'Account not found. Please check your email and try again.',
    'Password should be at least 6 characters': 'Password must meet the required complexity requirements.',
    'New password should be different from the old password': 'New password must be different from your current password.',
    'Email not confirmed': 'Please confirm your email address before updating your password.',
    'Too many requests': 'Too many attempts. Please wait a moment before trying again.',
    'Network error': 'Connection error. Please check your internet connection and try again.',
    
    // Common error patterns (exact matches only)
    'JWT expired': 'Your session has expired. Please request a new password reset link.',
    'Invalid JWT': 'Your session is invalid. Please request a new password reset link.',
    'User not found or not authorized': 'Account not found. Please check your email and try again.',
    'Password is too weak': 'Password must meet the required complexity requirements.',
    'Rate limit exceeded': 'Too many attempts. Please wait a moment before trying again.',
  };

  // Map error names/codes to user-friendly messages
  const errorCodeMap: Record<string, string> = {
    'AuthApiError': 'Authentication error. Please try again or contact support.',
    'AuthSessionMissingError': 'Your session has expired. Please request a new password reset link.',
    'AuthInvalidCredentialsError': 'Invalid credentials. Please check your information and try again.',
    'AuthUserNotFoundError': 'Account not found. Please check your email and try again.',
    'AuthWeakPasswordError': 'Password must meet the required complexity requirements.',
    'AuthTooManyRequestsError': 'Too many attempts. Please wait a moment before trying again.',
    'AuthNetworkError': 'Connection error. Please check your internet connection and try again.',
  };

  const errorMessage = error.message || '';
  const errorName = error.name || '';
  const errorStatus = error.status;

  // First, try exact message match
  if (errorMessageMap[errorMessage]) {
    return errorMessageMap[errorMessage];
  }

  // Then, try error name/code match
  if (errorCodeMap[errorName]) {
    return errorCodeMap[errorName];
  }

  // Handle specific HTTP status codes
  if (errorStatus === 401) {
    return 'Your session has expired. Please request a new password reset link.';
  }
  if (errorStatus === 403) {
    return 'Access denied. Please check your permissions and try again.';
  }
  if (errorStatus === 429) {
    return 'Too many attempts. Please wait a moment before trying again.';
  }
  if (errorStatus === 500) {
    return 'Server error. Please try again later or contact support.';
  }

  // For unknown errors, return a generic message
  return 'Unable to update password. Please try again or contact support if the problem persists.';
};

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json();
    
    if (!password) {
      return NextResponse.json(
        { error: { code: 'MISSING_PASSWORD', message: 'Password is required' } },
        { status: 400 }
      );
    }

    // Password validation function
    const validatePassword = (password: string) => {
      const errors: string[] = [];
      
      if (password.length < 8) {
        errors.push('at least 8 characters long');
      }
      
      if (!/[A-Z]/.test(password)) {
        errors.push('one uppercase letter');
      }
      
      if (!/[a-z]/.test(password)) {
        errors.push('one lowercase letter');
      }
      
      if (!/\d/.test(password)) {
        errors.push('one number');
      }
      
      if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        errors.push('one special character');
      }
      
      return errors;
    };

    const passwordErrors = validatePassword(password);
    
    if (passwordErrors.length > 0) {
      return NextResponse.json(
        { 
          error: { 
            code: 'INVALID_PASSWORD', 
            message: `Password must contain ${passwordErrors.join(', ')}` 
          } 
        },
        { status: 400 }
      );
    }

    const supabase = await createSupabaseServerClient();
    
    // Get the current session from the request
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: { code: 'NO_SESSION', message: 'No valid session found. Please use the reset link from your email.' } },
        { status: 401 }
      );
    }

    // Update the user's password
    const { error } = await supabase.auth.updateUser({
      password: password,
    });

    if (error) {
      // Log only safe error information, not the full error object
      console.error('Password update failed:', {
        code: error.name,
        message: error.message,
        status: error.status || 'unknown'
      });
      return NextResponse.json(
        { error: { code: 'UPDATE_FAILED', message: sanitizeErrorMessage(error) } },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Password updated successfully' },
      { status: 200 }
    );
  } catch (error) {
    // Log only safe error information, not the full error object
    console.error('Password update request failed:', {
      type: error instanceof Error ? error.constructor.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
    return NextResponse.json(
      { error: { code: 'SERVER_ERROR', message: 'An unexpected error occurred' } },
      { status: 500 }
    );
  }
}
