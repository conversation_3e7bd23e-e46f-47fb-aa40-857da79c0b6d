# CSS Dost - 7-Day Development Plan

This document outlines the 7-day development plan to build the MVP of the CSS Dost platform.

## 🚀 Day 1: Foundation & Authentication
- **Objective:** Set up the project and get user authentication working.
- **Tasks:**
1.  Initialize Next.js 15 project with TypeScript & Tailwind CSS: `npx create-next-app@latest . --typescript --tailwind --eslint`
    2.  Set up Prisma with PostgreSQL: `npx prisma init`
    3.  Define initial `User` and `Profile` models in `schema.prisma`.
    4.  Run initial migration: `npx prisma migrate dev --name init`
5.  Install and configure NextAuth.js to work with Supabase JWTs and our existing Supabase project.
6.  Implement sign-in/sign-out flows compatible with the backend APIs (reuse Supabase auth tokens for API access).
    7.  Create basic `Navbar`, `Footer`, and main `Layout` component.
    8.  Build `Login` and `Register` pages.

## 📚 Day 2: Content Structure & Display
- **Objective:** Build the core content browsing experience.
- **Tasks:**
    1.  Define `Subject`, `Material`, and `Video` models in `schema.prisma`.
    2.  Create seed script to populate subjects.
    3.  Build API routes to fetch subjects and their materials/videos.
    4.  Create a `[subject]` page to display materials and videos.
    5.  Design `SubjectCard` and `MaterialCard` components.
    6.  Implement basic UI for viewing materials (e.g., PDF viewer or markdown renderer).

## 📝 Day 3: Mock Tests (Backend)
- **Objective:** Create the data structure and logic for mock tests.
- **Tasks:**
    1.  Define `Test`, `Question`, `Option`, and `TestResult` models in `schema.prisma`.
    2.  Build API routes for starting a test, fetching questions, and submitting answers.
    3.  Implement server-side logic to calculate test scores.
    4.  Create seed data for a sample test.

## 🖥️ Day 4: Mock Tests (Frontend)
- **Objective:** Build the user interface for taking tests.
- **Tasks:**
    1.  Create a `[testId]` page to conduct the test.
    2.  Build a `TestRunner` component to manage test state (timer, current question).
    3.  Develop `QuestionCard` component to display questions and options.
    4.  Create a `TestResult` page to show user's score and a summary.

## 📈 Day 5: User Profile & Progress Tracking
- **Objective:** Allow users to track their performance.
- **Tasks:**
    1.  Create a `Dashboard` or `Profile` page for authenticated users.
    2.  Display a list of tests taken and their scores.
    3.  Implement basic charts to visualize progress (e.g., using `recharts`).
    4.  Fetch and integrate YouTube videos using the YouTube API on subject pages.

## 👑 Day 6: Admin Panel & Community
- **Objective:** Build basic content management and community features.
- **Tasks:**
    1.  Create a protected `/admin` route group for admin users.
    2.  Build a simple admin dashboard to manage subjects, materials, and tests (CRUD operations).
    3.  Define `Post` and `Comment` models in `schema.prisma` for the forum.
    4.  Create a basic `/forum` page to display posts and comments.

## 🚢 Day 7: Polish, SEO & Deployment
- **Objective:** Finalize the MVP and deploy it.
- **Tasks:**
    1.  Perform a full review of the UI, ensuring responsiveness and consistency.
    2.  Add metadata (title, description) to all pages for SEO using `generateMetadata`.
    3.  Set up a project on Vercel.
4.  Configure environment variables on Vercel (Supabase envs: `POSTGRES_PRISMA_URL`, `SUPABASE_URL`, `SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`, `SUPABASE_JWT_SECRET`, `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`).
    5.  Push to GitHub and trigger the first deployment.
    6.  Conduct final testing on the live environment. 