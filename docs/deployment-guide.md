# CSS Dost - Deployment Guide

This guide provides step-by-step instructions for deploying the CSS Dost application to Vercel.

## Prerequisites

1.  A Vercel account.
2.  A GitHub account.
3.  The project code pushed to a GitHub repository.

## Step 1: Set up the Vercel Project

1.  Go to your Vercel dashboard and click "Add New... > Project".
2.  Import the Git repository for this project.
3.  Vercel will automatically detect that it's a Next.js project. No changes are needed for the "Framework Preset" or "Build and Output Settings".

## Step 2: Configure Environment Variables

This is the most critical step. Add the following variables in Vercel (`Project → Settings → Environment Variables`). These align with the current Production env.

-   `POSTGRES_PRISMA_URL`
    -   Prisma connection string for the Supabase Postgres database.
    -   <PERSON><PERSON><PERSON> should read this value via `env("POSTGRES_PRISMA_URL")`.
    -   Optionally, also set `DATABASE_URL` with the same value if your local tooling expects it.

-   `POSTGRES_URL` and `POSTGRES_URL_NON_POOLING`
    -   Standard Supabase-provided Postgres URLs. Useful for non-Prisma tools, scripts, or direct connections.

-   `SUPABASE_URL`
    -   Your Supabase project URL (server-side).

-   `SUPABASE_ANON_KEY`
    -   Supabase anon key (server-side use allowed; prefer the NEXT_PUBLIC variant for the client).

-   `SUPABASE_SERVICE_ROLE_KEY`
    -   Supabase service role key (server-only). Never expose to the client.

-   `SUPABASE_JWT_SECRET`
    -   Used to validate Supabase-issued JWTs on your API.

-   `NEXT_PUBLIC_SUPABASE_URL`
    -   Same as `SUPABASE_URL`, but explicitly exposed to the client.

-   `NEXT_PUBLIC_SUPABASE_ANON_KEY`
    -   Same as `SUPABASE_ANON_KEY`, but explicitly exposed to the client.

-   `YOUTUBE_API_KEY`
    -   API key for YouTube Data API v3 (if fetching videos server-side).

### Future Setup: Vercel Blob (for File Storage)
*When we decide to host our own study materials instead of linking to them, we will use Vercel Blob.*

-   **`BLOB_READ_WRITE_TOKEN`**
    -   The read/write token for your Vercel Blob store. You will get this from the Vercel dashboard when you create a Blob store.

## Step 3: Deployment

1.  After configuring the environment variables, navigate to the "Deployments" tab for your project.
2.  Trigger a new deployment for the `main` branch.
3.  Vercel will build the project. Ensure Prisma is available and the client is generated (e.g., via `postinstall` or a build step). For API auth, the app consumes Supabase JWTs; no separate OAuth app is required for MVP.

## Step 4: Running Migrations

Vercel does not automatically run migrations. Run them manually against production after the first deploy:

1.  In local `.env.local`, set `POSTGRES_PRISMA_URL` to the production value temporarily (or export it in shell).
2.  Run:
    ```bash
    npx prisma migrate deploy
    ```
3.  Restore your local DB URL afterwards.

An alternative for advanced setups is to modify the build command in `package.json` to run migrations, but manual migration is safer for an MVP.

```json
// package.json (Example for custom build)
"scripts": {
  "build": "prisma generate && prisma migrate deploy && next build"
}
```
*Note: Using the custom build script is convenient but can be risky if a migration fails.*

## Step 5: Final Testing

- Once deployed, visit your production URL.
- Test all core functionalities:
  - User registration and login.
  - Viewing subjects and materials.
  - Taking a mock test.
  - Checking the user dashboard.
- Verify that all environment variables are working correctly. 