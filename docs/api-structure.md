# CSS Dost - API Structure

This document outlines the API endpoints for the CSS Dost platform, following RESTful principles.

## Base URL
`/api`

## Authentication
- Handled by <PERSON><PERSON><PERSON> (email/password and OAuth). Sessions managed via Supabase cookies and Next.js middleware.
- Required envs: `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`, `NEXT_PUBLIC_SITE_URL`.
- The `/auth/callback` route exchanges the code for a session.

## 📚 Subjects API

- **`GET /api/subjects`**
  - **Description:** Fetches a list of all subjects.
  - **Response Body:** `Subject[]`

- **`GET /api/subjects/:subjectId`**
  - **Description:** Fetches a single subject with its materials and videos.
  - **Response Body:** `Subject` with `materials` and `videos` included.

## 📝 Materials API

- **`GET /api/materials/:materialId`**
  - **Description:** Fetches a single material.
  - **Response Body:** `Material`

## 🎬 Videos API

- **`GET /api/videos/youtube?channelId=[ID]`**
  - **Description:** Fetches videos from a specific YouTube channel using the YouTube API. This will be a server-side route handler.
  - **Response Body:** `any[]` (YouTube API video format)

## 🧪 Mock Tests API

- **`POST /api/tests/start`**
  - **Description:** Starts a new test session for a user.
  - **Request Body:** `{ testId: string }`
  - **Response Body:** `{ testSessionId: string, questions: Question[] }` (Questions won't include the `isCorrect` field)

- **`POST /api/tests/submit`**
  - **Description:** Submits a completed test.
  - **Request Body:** `{ testSessionId: string, answers: { questionId: string, optionId: string }[] }`
  - **Response Body:** `{ testResultId: string, score: number }`

- **`GET /api/tests/results/:resultId`**
  - **Description:** Fetches the result of a specific test.
  - **Response Body:** `TestResult` with details about correct/incorrect answers.

## 👤 User Profile & Progress API

- **`GET /api/user/me`**
  - **Description:** Fetches the profile and progress for the currently authenticated user.
  - **Response Body:** `User` with `profile` and `testResults` included.

- **`PUT /api/user/profile`**
  - **Description:** Updates the user's profile.
  - **Request Body:** `{ bio: string }`
  - **Response Body:** `Profile`

## 💬 Forum API

- **`GET /api/posts`**
  - **Description:** Fetches all forum posts.
  - **Response Body:** `Post[]` with `author` included.

- **`GET /api/posts/:postId`**
  - **Description:** Fetches a single post with its comments.
  - **Response Body:** `Post` with `comments` and `author` included.

- **`POST /api/posts`**
  - **Description:** Creates a new forum post.
  - **Request Body:** `{ title: string, content: string }`
  - **Response Body:** `Post`

- **`POST /api/posts/:postId/comments`**
  - **Description:** Adds a comment to a post.
  - **Request Body:** `{ content: string }`
  - **Response Body:** `Comment`

## 👑 Admin API (Protected)

- All routes under `/api/admin/*` will be protected and only accessible by users with the `ADMIN` role.

- **`GET /api/admin/subjects`**: List all subjects.
- **`POST /api/admin/subjects`**: Create a new subject.
- **`PUT /api/admin/subjects/:subjectId`**: Update a subject.
- **`DELETE /api/admin/subjects/:subjectId`**: Delete a subject.

- (Similar CRUD endpoints will exist for `materials`, `tests`, `questions`, etc.) 