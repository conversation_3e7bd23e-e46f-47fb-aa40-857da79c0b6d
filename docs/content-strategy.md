# CSS Dost - Content Strategy

This document outlines the strategy for curating, managing, and structuring content for the CSS Dost platform.

## Guiding Principle
The content must be **relevant, up-to-date, and well-organized** to provide maximum value to CSS aspirants. We are curating, not creating from scratch for the MVP.

## 1. Subject Structure

The first step is to define the hierarchy of CSS subjects. We will categorize them into **Compulsory** and **Optional** groups.

### Compulsory Subjects (Initial Set)
1.  English Essay
2.  English (Precis & Composition)
3.  General Science & Ability
4.  Current Affairs
5.  Pakistan Affairs
6.  Islamic Studies (or Comparative Study of Major Religions)

### Optional Subjects (Examples for Initial Set)
-   **Group I:** International Relations, Political Science
-   **Group II:** Physics, Chemistry
-   **Group V:** History of USA, European History
-   *(We will start with a few popular optional subjects and expand later).*

This structure will be seeded into the `Subject` table in the database.

## 2. Content Curation Strategy

### Study Materials (PDFs, Notes, Articles)
-   **Source 1: Past Papers:** Gather official CSS past papers from the FPSC (Federal Public Service Commission) website. These are the highest priority.
-   **Source 2: Recommended Books:** Link to publicly available PDF versions of highly recommended textbooks for each subject.
-   **Source 3: Reputable Academies:** Curate free notes and resources shared by well-known CSS coaching academies.
-   **Storage:** For the MVP, we will link directly to external resources (`contentUrl`). We will not host files ourselves to save on storage costs and complexity.

### Video Content
-   **Source:** YouTube.
-   **Strategy:**
    1.  Identify reputable YouTube channels that provide high-quality lectures on CSS subjects.
    2.  Examples: Channels of famous instructors, educational institutions, or news analysis channels for current affairs.
    3.  Use the YouTube Data API to fetch playlists or videos related to specific subjects. This keeps the content fresh automatically.
    4.  Store the `youtubeUrl` in our `Video` table.

## 3. Mock Test Content

-   **Source:** Primarily from CSS past papers (MCQ sections).
-   **Creation Process:**
    1.  An admin will manually create a `Test` for a specific subject (e.g., "Pakistan Affairs - 2023 Past Paper MCQs").
    2.  The admin will then add `Question`s to this test, along with the `Option`s.
    3.  One option per question must be marked as `isCorrect`.
-   **Goal:** Build a solid bank of at least 5-10 mock tests from recent past papers for the launch.

## 4. Content Management

-   **Admin Panel:** All content (subjects, materials, videos, tests) will be managed through the `/admin` dashboard.
-   **Responsibilities of Admin:**
    -   Ensuring all links are active and not broken.
    -   Adding new content as it becomes available.
    -   Structuring tests correctly.
    -   Keeping subject lists updated based on FPSC syllabus changes.

## 5. Future-Proofing

-   While we are linking externally for the MVP, the database schema allows for hosting our own content in the future.
-   The modular subject design means we can easily add or remove optional subjects without major code changes. 