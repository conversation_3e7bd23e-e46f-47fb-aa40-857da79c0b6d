# CSS Dost - UI Components

This document outlines the component hierarchy for the CSS Dost platform. We will use Tailwind CSS for styling and strive for a consistent, modern design.

## 🎨 Design System & Theming

- **Primary Color:** A professional blue (`#005A9C`)
- **Secondary Color:** A neutral gray (`#F0F2F5`)
- **Typography:** `Inter` font, sourced from Google Fonts via `next/font`.
- **Layout:** Consistent padding and spacing using Tailwind's spacing scale.

## 📁 Component Structure (`/components`)

### Layout Components (`/components/layout`)

- **`Navbar.tsx`**: Top navigation bar. Shows user profile/login button, links to subjects, forum, etc.
- **`Footer.tsx`**: Site footer with links and copyright info.
- **`Sidebar.tsx`**: (For admin/dashboard) Navigation sidebar.
- **`MainLayout.tsx`**: Wraps pages with Navbar and Footer.

### UI Primitives (`/components/ui`)

- **`Button.tsx`**: Standard button with variants (primary, secondary, danger).
- **`Card.tsx`**: A generic card component for wrapping content.
- **`Input.tsx`**: Styled input field.
- **`Modal.tsx`**: For pop-up dialogs.
- **`Spinner.tsx`**: Loading indicator.

### Feature Components (`/components/features`)

#### Content (`/components/features/content`)

- **`SubjectCard.tsx`**: Displays a single CSS subject on the main subjects page.
- **`MaterialCard.tsx`**: Displays a single study material (e.g., PDF link).
- **`VideoCard.tsx`**: Displays a single YouTube video thumbnail and title.

#### Tests (`/components/features/tests`)

- **`TestRunner.tsx`**: The main component for the test-taking experience. Manages state like timer, current question index, and user answers.
- **`QuestionCard.tsx`**: Renders a single question with its options.
- **`TestResultSummary.tsx`**: Shows the final score and a brief summary after a test is completed.

#### User (`/components/features/user`)

- **`UserProfile.tsx`**: Displays user information.
- **`ProgressChart.tsx`**: A chart to visualize test score trends.
- **`AuthButtons.tsx`**: Client component showing Login/Register or Logout/Dashboard buttons based on session status.

#### Forum (`/components/features/forum`)

- **`PostCard.tsx`**: Displays a summary of a forum post.
- **`Comment.tsx`**: Displays a single comment.

#### Admin (`/components/features/admin`)

- **`AdminDashboard.tsx`**: The main interface for admin tasks.
- **`ContentTable.tsx`**: A reusable table to display and manage content (subjects, materials, etc.).
- **`ContentForm.tsx`**: A form to create/edit content. 