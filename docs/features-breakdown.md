# CSS Dost - Features Breakdown

This document provides a detailed breakdown of each core feature for the MVP.

## 1. User Authentication & Profiles

- **Provider:** Supabase Auth
- **Methods:** Email/Password, Google/GitHub OAuth.
- **User Flow:**
  - Users can register and log in.
  - After login, users are redirected to their dashboard.
  - A user profile page (`/dashboard`) will display their name, email, and a list of their test results.
- **Roles:** `USER` and `ADMIN`. Admins will have access to a separate admin panel.

## 2. Subject-wise Study Materials

- **Structure:**
  - A main page (`/subjects`) lists all available subjects (e.g., Pakistan Affairs, English Essay).
  - Each subject has a dynamic page (`/subjects/[subject-slug]`).
- **Content:**
  - Each subject page will have two tabs: "Study Materials" and "Videos".
  - **Materials:** Links to curated PDFs, articles, or notes. Displayed as a list of cards.
  - **Videos:** Embedded YouTube videos, fetched via the YouTube API.

## 3. Interactive Mock Tests & Quizzes

- **Test Format:** Primarily Multiple Choice Questions (MCQs).
- **User Flow:**
  1.  User selects a test from a subject page.
  2.  A test page (`/tests/[testId]`) starts a timed session.
  3.  The `TestRunner` component fetches questions and displays them one by one.
  4.  User selects an option for each question.
  5.  Upon submission (or when the timer ends), the answers are sent to the server.
  6.  The server calculates the score and saves a `TestResult`.
  7.  User is redirected to a results page (`/results/[resultId]`) showing their score.

## 4. User Progress Tracking

- **Dashboard:** The user's dashboard will be the central hub for tracking.
- **Features:**
  - A list of all tests the user has attempted.
  - Each entry will show the test name, date taken, and score.
  - A simple line chart will visualize the scores over time for each subject, showing progress.

## 5. Community Forum (Basic)

- **Functionality:**
  - A main forum page (`/forum`) that lists all posts.
  - Users can click a post to view its content and comments.
  - Authenticated users can create new posts.
  - Authenticated users can comment on existing posts.
- **Limitations for MVP:** No complex features like upvoting, moderation (beyond admin deletion), or user tagging.

## 6. Admin Panel

- **Access:** Restricted to users with the `ADMIN` role via middleware.
- **Location:** `/admin`
- **Features:**
  - Full CRUD (Create, Read, Update, Delete) operations for:
    - Subjects
    - Study Materials
    - Tests
    - Questions and Options
- **Interface:** Simple, table-based layouts for managing content. 