# CSS Dost — User Stories and Acceptance Criteria

This document defines detailed user stories for the CSS Dost MVP and near-term roadmap, including acceptance criteria and edge cases. It is intended to guide both the web app and the future mobile app (React Native) that will reuse the same backend APIs.

Roles used below come from the current schema and plan:
- Guest (Unauthenticated)
- User (Authenticated Student)
- <PERSON><PERSON> (Administrator)

Assumptions:
- Auth is provided by Supabase (email/password + OAuth). Sessions are handled via Supabase cookies and Next.js middleware.
- Backend uses REST under `/api` and Prisma models in `schema.prisma`.

## 1) Authentication & Account

### 1.1 As a Guest, I can register with email/password
- Acceptance criteria
  - Given I am on the registration screen, when I submit a valid email and password, then my account is created and I am signed in.
  - Password policy: at least 8 chars; server validates and returns a clear error if invalid.
  - If email already exists, I get a 409 conflict with a friendly message.
- Edge cases
  - Network interruption during submit → clear error; form remains populated.
  - Duplicate rapid submits → server safely prevents duplicate user creation (idempotent-by-unique-email).

### 1.2 As a Guest, I can sign in with email/password
- Acceptance criteria
  - Valid credentials → 200 OK, authenticated session established; redirected to dashboard.
  - Invalid credentials → 401 with generic message (do not reveal whether email exists).
- Edge cases
  - Rate limiting after repeated failures.

### 1.3 As a Guest, I can sign in with Google
- Acceptance criteria
  - Clicking Google starts OAuth; on success, account is created (if new) and I am signed in.
  - If email is already in use by credentials auth, accounts are linked or a clear resolution path is presented (config-dependent; MVP can disallow linking and return a message to sign in with original method).

### 1.4 As a User, I can sign out
- Acceptance criteria
  - Clicking sign out clears session server-side and client cookies; I am redirected to the home page.

### 1.5 As a User, I can view and edit my profile (bio, name, image)
- Acceptance criteria
  - GET `/api/user/me` returns current user with profile and test results summary.
  - PUT `/api/user/profile` updates `bio` (and optionally `name`, `image`), returns updated profile.
  - Only the owner can update their profile; Admin cannot change user profile via this endpoint.
- Edge cases
  - Large/bad payloads → 400 validation error.

### 1.6 (Future) I can request a password reset link
- Acceptance criteria
  - POST `/api/auth/password/reset` accepts email; sends email if account exists; always 200 to avoid user enumeration.

## 2) Subjects & Content Browsing

### 2.1 As a Guest, I can browse a list of subjects
- Acceptance criteria
  - GET `/api/subjects` returns paginated list with `{ id, title, description }`.
  - Web shows a grid of `SubjectCard`s.
- Edge cases
  - Empty database shows an empty state UI.

### 2.2 As a Guest, I can view a subject’s details
- Acceptance criteria
  - GET `/api/subjects/:subjectId` returns subject with `materials[]` and `videos[]`.
  - Materials contain `title`, `contentUrl`, `contentType`.
  - Videos contain `title`, `youtubeUrl`.
- Edge cases
  - Invalid `subjectId` → 404.

### 2.3 As a Guest, I can open a study material
- Acceptance criteria
  - Clicking a material opens the external `contentUrl` in a new tab (web) or an in-app browser (mobile).
  - No file hosting in MVP; links are external.

### 2.4 As a Guest, I can view videos for a subject
- Acceptance criteria
  - YouTube URLs are embedded on web; deep-link capable on mobile.
  - Optional server route to fetch list via YouTube API if dynamic.

### 2.5 As a Guest/User, I can search/filter subjects and materials
- Acceptance criteria
  - Query params supported: `q`, `page`, `limit`, `sort`.
  - Search matches title and description (basic LIKE for MVP).

## 3) Mock Tests & Quizzes

### 3.1 As a User, I can list tests for a subject
- Acceptance criteria
  - GET `/api/subjects/:subjectId` includes `tests[]` or a separate `/api/tests?subjectId=...`.
  - Each test: `{ id, title, subjectId }`.

### 3.2 As a User, I can start a test
- Acceptance criteria
  - POST `/api/tests/start` with `{ testId }` returns `{ testSessionId, questions }` (questions exclude `isCorrect`).
  - Server sets a `startTime` and a `duration` (if timed) on the session.
- Edge cases
  - Starting multiple active sessions for same test returns the current active one for MVP or allows multiple (defined behavior must be documented; MVP: allow multiple, store all results).

### 3.3 As a User, I can answer questions and submit the test
- Acceptance criteria
  - POST `/api/tests/submit` with `{ testSessionId, answers: [{ questionId, optionId }] }` calculates score and persists `TestResult`.
  - Response contains `{ testResultId, score }`.
  - Score equals the count of correct answers / total questions.
- Edge cases
  - Missing or malformed answers → 400 with validation details.
  - Submitting twice → 409 or returns the prior result (idempotent submission per session).
  - Expired timer → 400 with `reason: "expired"`.

### 3.4 As a User, I can view my past test results
- Acceptance criteria
  - GET `/api/tests/results/:resultId` returns the result if I am the owner; Admin can access any.
  - Dashboard lists results with `{ testTitle, score, createdAt }`.

### 3.5 As a User, I can retake a test
- Acceptance criteria
  - Starting the same test later creates a new session and result; historical results are preserved.

### 3.6 As a User, my progress is tracked
- Acceptance criteria
  - `TestResult` stored with `userId`, `testId`, `score`, `createdAt`.
  - Dashboard aggregates recent scores and provides data for a simple chart.

### 3.7 Robustness during testing
- Edge cases
  - Refresh mid-test → client can refetch current session by `testSessionId` (optional MVP). If not implemented, refresh discards client state and user may restart.
  - Network loss while answering → answers buffered locally until submit; server validates on submit only.

## 4) Forum (Basic)

### 4.1 As a Guest, I can view posts and comments
- Acceptance criteria
  - GET `/api/posts` returns paginated posts with author summary.
  - GET `/api/posts/:postId` returns post with comments.
- Edge cases
  - 404 for unknown post.

### 4.2 As a User, I can create posts and comment
- Acceptance criteria
  - POST `/api/posts` with `{ title, content }` creates a post attributed to me.
  - POST `/api/posts/:postId/comments` with `{ content }` creates a comment.
  - Empty or too long content → 400.

### 4.3 As a User, I can edit/delete my own posts/comments
- Acceptance criteria
  - PUT/DELETE endpoints only allowed for resource owner or Admin.

### 4.4 As an Admin, I can moderate posts/comments
- Acceptance criteria
  - Admin can delete any post/comment.

## 5) Admin Panel & Content Management

### 5.1 As an Admin, I can manage subjects
- Acceptance criteria
  - CRUD endpoints under `/api/admin/subjects`.
  - Validation: title required and unique; description optional.

### 5.2 As an Admin, I can manage materials and videos
- Acceptance criteria
  - CRUD under `/api/admin/materials` and `/api/admin/videos` (or nested under subject routes).
  - Validation: `contentUrl` must be a valid URL; `youtubeUrl` must be a valid YouTube URL.

### 5.3 As an Admin, I can manage tests, questions, and options
- Acceptance criteria
  - CRUD under `/api/admin/tests`, `/api/admin/questions`, `/api/admin/options`.
  - One `Option.isCorrect` per question enforced server-side.

### 5.4 As an Admin, I can view a basic content dashboard
- Acceptance criteria
  - Summary counts: subjects, materials, tests, users, posts.

## 6) Mobile App (React Native) Considerations

- The backend API will be shared by web and mobile. Requirements:
  - Stable REST endpoints with explicit versioning for public clients (consider `/api/v1/*`).
  - JSON responses use a consistent envelope: `{ data, error }` (MVP can keep raw objects but define a consistent error shape).
  - Pagination: cursor-based when lists may grow large (`cursor`, `limit`).
  - Auth: mobile clients use secure token storage; server validates JWTs; CORS enabled for mobile dev origins if needed.
  - Idempotency: creation endpoints should be robust to retries (clients may retry on flaky networks).
  - Rate limiting and abuse protection for public endpoints (especially forum and auth routes).
  - Error messages localized client-side; server returns codes + keys where possible.

## 7) Non-Functional Requirements

- Security
  - Server-side validation on all inputs; sanitize strings; enforce auth and role checks on protected routes.
  - Protect secrets and never expose service keys to the client.
- Performance & Reliability
  - Paginate list endpoints; include basic indexes where needed (e.g., on foreign keys).
  - Implement sensible timeouts and avoid N+1 queries.
- Observability
  - Log request errors with correlation ids; redact PII in logs.
- Accessibility (web)
  - Keyboard navigation, ARIA labels for interactive components.

## 8) API Conventions (MVP)

- Status codes
  - 200 for success, 201 for creation, 400 for validation errors, 401 unauthenticated, 403 unauthorized, 404 not found, 409 conflict, 500 server error.
- Error format
  - `{ error: { code: string, message: string, details?: any } }`.
- Pagination
  - Query: `?cursor=...&limit=...`; Response: `{ items: T[], nextCursor?: string }`.
- Sorting/Filtering
  - `?sort=createdAt:desc&filter[field]=value` (MVP: limited filters as needed).

## 9) Data Privacy & Retention (MVP)

- Users can request profile deletion (future); Admin can remove content violating guidelines.
- Minimal PII stored: name, email, optional image and bio.

## 10) Out-of-Scope (MVP)

- Advanced moderation (reports, flags, shadow bans).
- Rich text uploads/attachments for forum posts.
- Payment/subscription.
- Full-blown analytics; only basic counts/charts for now.

---

This document should be kept in sync with `features-breakdown.md`, `api-structure.md`, and `schema.prisma` as the project evolves. For each implemented endpoint, add concrete request/response examples and expand acceptance criteria with exact validation rules.
