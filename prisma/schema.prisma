// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_PRISMA_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  role          UserRole  @default(USER)
  profile       Profile?
  testResults   TestResult[]
  posts         Post[]
  comments      Comment[]
}

model Profile {
  id     String @id @default(cuid())
  bio    String?
  user   User   @relation(fields: [userId], references: [id])
  userId String @unique
}

enum UserRole {
  USER
  ADMIN
}

// Authentication is handled via Supabase; no NextAuth-specific tables required

// Content Structure
model Subject {
  id        String     @id @default(cuid())
  title     String     @unique
  description String?
  materials Material[]
  videos    Video[]
  tests     Test[]
}

model Material {
  id          String   @id @default(cuid())
  title       String
  contentUrl  String // URL to PDF or other material
  contentType String   @default("PDF")
  subject     Subject  @relation(fields: [subjectId], references: [id])
  subjectId   String
}

model Video {
  id          String  @id @default(cuid())
  title       String
  youtubeUrl  String
  subject     Subject @relation(fields: [subjectId], references: [id])
  subjectId   String
}


// Mock Tests
model Test {
  id          String       @id @default(cuid())
  title       String
  subject     Subject      @relation(fields: [subjectId], references: [id])
  subjectId   String
  questions   Question[]
  testResults TestResult[]
}

model Question {
  id        String   @id @default(cuid())
  text      String
  test      Test     @relation(fields: [testId], references: [id])
  testId    String
  options   Option[]
}

model Option {
  id         String   @id @default(cuid())
  text       String
  isCorrect  Boolean  @default(false)
  question   Question @relation(fields: [questionId], references: [id])
  questionId String
}

model TestResult {
  id        String   @id @default(cuid())
  score     Float
  test      Test     @relation(fields: [testId], references: [id])
  testId    String
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  createdAt DateTime @default(now())
}


// Community / Forum
model Post {
  id        String    @id @default(cuid())
  title     String
  content   String
  author    User      @relation(fields: [authorId], references: [id])
  authorId  String
  comments  Comment[]
  createdAt DateTime  @default(now())
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  author    User     @relation(fields: [authorId], references: [id])
  authorId  String
  post      Post     @relation(fields: [postId], references: [id])
  postId    String
  createdAt DateTime @default(now())
}
